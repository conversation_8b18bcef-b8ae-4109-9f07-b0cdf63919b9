# تمسيك - منصة إسلامية شاملة لإعداد الخطب

منصة تمسيك هي منصة إسلامية متكاملة تهدف إلى مساعدة الخطباء والدعاة في إعداد وتنظيم خطبهم ودروسهم بطريقة احترافية مع نظام اقتراحات ذكي.

## 🌟 الميزات الرئيسية

### 📝 نظام إعداد الخطب المتطور
- **هيكل منظم**: تنظيم الخطبة وفقاً للبنية الإسلامية التقليدية
- **نظام الاقتراحات الذكي**: اقتراحات من المستخدمين السابقين للآيات والأحاديث والأدعية
- **حفظ تلقائي**: حفظ المسودات تلقائياً أثناء الكتابة
- **تصدير متعدد**: تصدير إلى Word أو طباعة مباشرة
- **معاينة فورية**: معاينة الخطبة قبل الحفظ النهائي

### 📚 مكتبة المحتوى الإسلامي
- **الآيات القرآنية**: مصنفة حسب الموضوع والسياق (أمر، وعد، إخبار)
- **الأحاديث الشريفة**: مع التخريج والتصنيف
- **الأدعية**: أدعية قرآنية ونبوية ومأثورة
- **السجع والخطابة**: نصوص بلاغية للخطباء
- **الشعر الإسلامي**: أبيات شعرية مناسبة للخطب
- **الآثار والأقوال**: أقوال الصحابة والتابعين

### 🎯 ميزات إضافية
- **الخطب الجاهزة**: مكتبة من الخطب الجاهزة في مواضيع متنوعة
- **العلماء اليمنيين**: فتاوى وآراء كبار العلماء اليمنيين
- **المفكرون والدعاة**: مقالات ومحاضرات المفكرين المعاصرين
- **المحاضرات والدروس**: جدولة ومتابعة المحاضرات في محافظات اليمن

## 🛠️ التقنيات المستخدمة

- **Backend**: Node.js, Express.js
- **Database**: SQLite (افتراضي) / MySQL
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Authentication**: JWT
- **File Upload**: Multer
- **Database Adapter**: دعم متعدد لقواعد البيانات

## 🚀 التثبيت والتشغيل

### المتطلبات
- Node.js (الإصدار 14 أو أحدث)
- npm أو yarn

### خطوات التثبيت السريعة

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd tamsik
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **تشغيل النظام مباشرة (SQLite)**
```bash
npm start
```

4. **الوصول للموقع**
```
http://localhost:3000
```

### إعداد MySQL (اختياري)

1. **تحديث ملف .env**
```env
DB_TYPE=mysql
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=tamsik_db
```

2. **إعداد قاعدة البيانات**
```bash
npm run setup-db
```

## 📖 دليل الاستخدام

### إعداد خطبة جديدة

1. **انتقل إلى صفحة إعداد الخطب**: `/prepare_sermon.html`
2. **املأ العنوان الرئيسي** للخطبة
3. **أضف المقدمة** مع الآيات أو الأحاديث المناسبة
4. **استخدم نظام الاقتراحات**: انقر على زر "اقتراحات" بجانب أي حقل
5. **أكمل أقسام الخطبة**: أما بعد، نص الخطبة، الخاتمة، الدعاء
6. **احفظ أو صدّر** الخطبة

### استخدام نظام الاقتراحات

- **انقر على زر "اقتراحات"** بجانب أي حقل نص
- **ابحث في الاقتراحات** باستخدام مربع البحث
- **اختر الاقتراح المناسب** بالنقر عليه
- **انقر "استخدام هذا الاقتراح"** لإدراجه في الحقل

## 🗂️ هيكل المشروع

```
tamsik/
├── config/              # إعدادات قاعدة البيانات
│   ├── database.js      # إعدادات MySQL
│   ├── database-adapter.js  # محول قواعد البيانات
│   └── sqlite-setup.js  # إعداد SQLite
├── models/              # نماذج قاعدة البيانات
│   ├── SermonSuggestions.js  # نموذج الاقتراحات
│   └── ...
├── routes/              # مسارات API
│   ├── suggestions.js   # API الاقتراحات
│   └── ...
├── public/              # الملفات الثابتة
│   ├── prepare_sermon.html   # صفحة إعداد الخطب
│   ├── js/prepare-sermon.js  # JavaScript الخاص بالصفحة
│   └── ...
├── scripts/             # سكريبتات مساعدة
│   └── addInitialSuggestions.js  # إضافة بيانات أولية
└── server.js            # نقطة دخول التطبيق
```

## 🔌 API Documentation

### نظام الاقتراحات

#### الآيات القرآنية
```http
GET /api/suggestions/verses?context_type=أمر&topic=التقوى&limit=10
POST /api/suggestions/verses
```

#### الأحاديث الشريفة
```http
GET /api/suggestions/hadith?context_type=وعد&limit=10
POST /api/suggestions/hadith
```

#### الأدعية
```http
GET /api/suggestions/dua?dua_type=ثناء&limit=10
POST /api/suggestions/dua
```

#### السجع والشعر
```http
GET /api/suggestions/saja?topic=التقوى&rhyme=الله
GET /api/suggestions/poetry?poet=السموأل&rhyme=جميل
```

### المصادقة والمستخدمين
```http
POST /api/auth/login
POST /api/auth/register
GET /api/users/profile
```

### الخطب والمحتوى
```http
GET /api/sermons
POST /api/sermons
GET /api/scholars
GET /api/fatwas
```

## 📊 إحصائيات قاعدة البيانات

النظام يحتوي على بيانات أولية شاملة:
- **الآيات القرآنية**: 10+ آية مصنفة
- **الأحاديث الشريفة**: 6+ أحاديث مخرجة
- **الأدعية**: 11+ دعاء متنوع
- **السجع**: 2+ نص بلاغي
- **الشعر الإسلامي**: 3+ قصائد
- **الآثار والأقوال**: 2+ أثر مأثور

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير المنصة:

1. **Fork المشروع**
2. **إنشاء فرع جديد**: `git checkout -b feature/amazing-feature`
3. **Commit التغييرات**: `git commit -m 'Add amazing feature'`
4. **Push للفرع**: `git push origin feature/amazing-feature`
5. **فتح Pull Request**

### إضافة محتوى إسلامي

يمكنك المساهمة بإضافة:
- آيات قرآنية جديدة مع تصنيفها
- أحاديث شريفة مع التخريج
- أدعية مأثورة
- نصوص بلاغية وشعر إسلامي

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: [tamsik.com](https://tamsik.com)

---

**تمسيك** - "والذين يمسكون بالكتاب وأقاموا الصلاة إنا لا نضيع أجر المصلحين"

*منصة إسلامية متكاملة لخدمة الخطباء والدعاة في العالم الإسلامي*
