{"timestamp": "2025-06-23T09:47:19.858Z", "level": "error", "message": "Uncaught Exception", "error": {"name": "Error", "message": "listen EADDRINUSE: address already in use :::3000", "stack": "Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (C:\\Users\\<USER>\\OneDrive\\Desktop\\Tamsik\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\Tamsik\\server.js:119:13)"}, "request": null}